import 'user.dart';
import 'message.dart';

enum GroupChatRole {
  admin,
  moderator,
  member,
}

class GroupChatMember {
  final String userId;
  final String name;
  final String? avatar;
  final GroupChatRole role;
  final DateTime joinedAt;
  final bool isOnline;
  final DateTime? lastSeen;

  const GroupChatMember({
    required this.userId,
    required this.name,
    this.avatar,
    this.role = GroupChatRole.member,
    required this.joinedAt,
    this.isOnline = false,
    this.lastSeen,
  });

  GroupChatMember copyWith({
    String? userId,
    String? name,
    String? avatar,
    GroupChatRole? role,
    DateTime? joinedAt,
    bool? isOnline,
    DateTime? lastSeen,
  }) {
    return GroupChatMember(
      userId: userId ?? this.userId,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      role: role ?? this.role,
      joinedAt: joinedAt ?? this.joinedAt,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'name': name,
      'avatar': avatar,
      'role': role.name,
      'joinedAt': joinedAt.toIso8601String(),
      'isOnline': isOnline,
      'lastSeen': lastSeen?.toIso8601String(),
    };
  }

  factory GroupChatMember.fromJson(Map<String, dynamic> json) {
    return GroupChatMember(
      userId: json['userId'],
      name: json['name'],
      avatar: json['avatar'],
      role: GroupChatRole.values.firstWhere(
        (e) => e.name == json['role'],
        orElse: () => GroupChatRole.member,
      ),
      joinedAt: DateTime.parse(json['joinedAt']),
      isOnline: json['isOnline'] ?? false,
      lastSeen: json['lastSeen'] != null 
          ? DateTime.parse(json['lastSeen'])
          : null,
    );
  }
}

class GroupChat {
  final String id;
  final String name;
  final String? description;
  final String? avatar;
  final List<GroupChatMember> members;
  final Message? lastMessage;
  final int unreadCount;
  final DateTime lastActivity;
  final DateTime createdAt;
  final String createdBy;
  final bool isPinned;
  final bool isMuted;
  final Map<String, bool> typingUsers; // userId -> isTyping

  const GroupChat({
    required this.id,
    required this.name,
    this.description,
    this.avatar,
    required this.members,
    this.lastMessage,
    this.unreadCount = 0,
    required this.lastActivity,
    required this.createdAt,
    required this.createdBy,
    this.isPinned = false,
    this.isMuted = false,
    this.typingUsers = const {},
  });

  GroupChat copyWith({
    String? id,
    String? name,
    String? description,
    String? avatar,
    List<GroupChatMember>? members,
    Message? lastMessage,
    int? unreadCount,
    DateTime? lastActivity,
    DateTime? createdAt,
    String? createdBy,
    bool? isPinned,
    bool? isMuted,
    Map<String, bool>? typingUsers,
  }) {
    return GroupChat(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      avatar: avatar ?? this.avatar,
      members: members ?? this.members,
      lastMessage: lastMessage ?? this.lastMessage,
      unreadCount: unreadCount ?? this.unreadCount,
      lastActivity: lastActivity ?? this.lastActivity,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      isPinned: isPinned ?? this.isPinned,
      isMuted: isMuted ?? this.isMuted,
      typingUsers: typingUsers ?? this.typingUsers,
    );
  }

  // دوال مساعدة
  bool isUserAdmin(String userId) {
    final member = members.firstWhere(
      (m) => m.userId == userId,
      orElse: () => GroupChatMember(
        userId: '',
        name: '',
        joinedAt: DateTime.now(),
      ),
    );
    return member.role == GroupChatRole.admin;
  }

  bool isUserMember(String userId) {
    return members.any((m) => m.userId == userId);
  }

  int get onlineMembersCount {
    return members.where((m) => m.isOnline).length;
  }

  List<String> get typingUserNames {
    return typingUsers.entries
        .where((entry) => entry.value)
        .map((entry) {
          final member = members.firstWhere(
            (m) => m.userId == entry.key,
            orElse: () => GroupChatMember(
              userId: '',
              name: 'مستخدم',
              joinedAt: DateTime.now(),
            ),
          );
          return member.name;
        })
        .toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'avatar': avatar,
      'members': members.map((m) => m.toJson()).toList(),
      'lastMessage': lastMessage?.toJson(),
      'unreadCount': unreadCount,
      'lastActivity': lastActivity.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'isPinned': isPinned,
      'isMuted': isMuted,
      'typingUsers': typingUsers,
    };
  }

  factory GroupChat.fromJson(Map<String, dynamic> json) {
    return GroupChat(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      avatar: json['avatar'],
      members: (json['members'] as List<dynamic>?)
          ?.map((m) => GroupChatMember.fromJson(m))
          .toList() ?? [],
      lastMessage: json['lastMessage'] != null 
          ? Message.fromJson(json['lastMessage'])
          : null,
      unreadCount: json['unreadCount'] ?? 0,
      lastActivity: DateTime.parse(json['lastActivity']),
      createdAt: DateTime.parse(json['createdAt']),
      createdBy: json['createdBy'],
      isPinned: json['isPinned'] ?? false,
      isMuted: json['isMuted'] ?? false,
      typingUsers: Map<String, bool>.from(json['typingUsers'] ?? {}),
    );
  }
}
