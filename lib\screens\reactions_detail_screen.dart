import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post.dart';
import '../models/reaction_types.dart';
import '../providers/social_provider.dart';
import '../widgets/professional_reactions.dart';
import '../widgets/smart_avatar.dart';
import 'facebook_profile_screen.dart';

class ReactionsDetailScreen extends StatefulWidget {
  final Post post;

  const ReactionsDetailScreen({
    super.key,
    required this.post,
  });

  @override
  State<ReactionsDetailScreen> createState() => _ReactionsDetailScreenState();
}

class _ReactionsDetailScreenState extends State<ReactionsDetailScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  Map<ReactionType, List<PostReaction>> _groupedReactions = {};
  int _totalReactions = 0;

  @override
  void initState() {
    super.initState();
    _groupReactions();
    _tabController = TabController(
      length: _groupedReactions.length + 1, // +1 للتبويب "الكل"
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _groupReactions() {
    _groupedReactions.clear();
    _totalReactions = widget.post.reactions.length;

    for (final reaction in widget.post.reactions) {
      final reactionType = ReactionType.values.firstWhere(
        (type) => type.name == reaction.type,
        orElse: () => ReactionType.like,
      );

      if (_groupedReactions[reactionType] == null) {
        _groupedReactions[reactionType] = [];
      }
      _groupedReactions[reactionType]!.add(reaction);
    }

    // ترتيب التفاعلات حسب العدد
    final sortedEntries = _groupedReactions.entries.toList();
    sortedEntries.sort((a, b) => b.value.length.compareTo(a.value.length));
    _groupedReactions = Map.fromEntries(sortedEntries);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'التفاعلات',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: PreferredSize(
          preferredSize: const Size(double.infinity, 60),
          child: _buildTabBar(),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllReactionsTab(),
          ..._groupedReactions.entries.map((entry) =>
              _buildReactionTab(entry.key, entry.value)),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: const Color(0xFF1877F2),
        indicatorWeight: 3,
        labelColor: const Color(0xFF1877F2),
        unselectedLabelColor: Colors.grey[600],
        tabs: [
          // تبويب "الكل"
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('الكل'),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '$_totalReactions',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // تبويبات التفاعلات
          ..._groupedReactions.entries.map((entry) {
            final reactionData = ReactionData.getReaction(entry.key);
            return Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ProfessionalReactionIcon(
                    type: entry.key,
                    size: 20,
                    showShadow: false,
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: reactionData.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${entry.value.length}',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: reactionData.color,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildAllReactionsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.post.reactions.length,
      itemBuilder: (context, index) {
        final reaction = widget.post.reactions[index];
        final reactionType = ReactionType.values.firstWhere(
          (type) => type.name == reaction.type,
          orElse: () => ReactionType.like,
        );
        
        return _buildReactionItem(reaction, reactionType);
      },
    );
  }

  Widget _buildReactionTab(ReactionType type, List<PostReaction> reactions) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: reactions.length,
      itemBuilder: (context, index) {
        final reaction = reactions[index];
        return _buildReactionItem(reaction, type);
      },
    );
  }

  Widget _buildReactionItem(PostReaction reaction, ReactionType type) {
    final reactionData = ReactionData.getReaction(type);
    final userName = _getUserName(reaction.userId);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _navigateToProfile(reaction.userId),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // الصورة الشخصية
              Stack(
                children: [
                  SmartAvatar(
                    name: userName,
                    radius: 24,
                  ),
                  // أيقونة التفاعل
                  Positioned(
                    bottom: -2,
                    right: -2,
                    child: ProfessionalReactionIcon(
                      type: type,
                      size: 16,
                      showShadow: true,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(width: 12),
              
              // معلومات المستخدم
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _getTimeAgo(reaction.timestamp),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              
              // نوع التفاعل
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: reactionData.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: reactionData.color.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ProfessionalReactionIcon(
                      type: type,
                      size: 16,
                      showShadow: false,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      reactionData.name,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: reactionData.color,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToProfile(String userId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FacebookProfileScreen(
          userId: userId,
          userName: _getUserName(userId),
        ),
      ),
    );
  }

  String _getUserName(String userId) {
    switch (userId) {
      case '1': return 'أحمد محمد';
      case '2': return 'فاطمة علي';
      case '3': return 'محمد حسن';
      case '4': return 'عائشة أحمد';
      case '5': return 'عمر خالد';
      case 'current_user': return 'أنت';
      default: return 'مستخدم';
    }
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}
