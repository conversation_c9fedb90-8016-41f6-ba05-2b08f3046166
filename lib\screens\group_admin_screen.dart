import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/group.dart';
import '../providers/social_provider.dart';
import '../widgets/group_members_tab.dart';
import 'group_settings_screen.dart';

class GroupAdminScreen extends StatefulWidget {
  final Group group;

  const GroupAdminScreen({
    super.key,
    required this.group,
  });

  @override
  State<GroupAdminScreen> createState() => _GroupAdminScreenState();
}

class _GroupAdminScreenState extends State<GroupAdminScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'إدارة ${widget.group.name}',
          style: const TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.black),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => GroupSettingsScreen(group: widget.group),
                ),
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.blue,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.blue,
          tabs: const [
            Tab(text: 'الأعضاء'),
            Tab(text: 'الطلبات'),
            Tab(text: 'الأنشطة'),
            Tab(text: 'الإحصائيات'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMembersTab(),
          _buildRequestsTab(),
          _buildActivitiesTab(),
          _buildStatsTab(),
        ],
      ),
    );
  }

  Widget _buildMembersTab() {
    return GroupMembersTab(group: widget.group);
  }

  Widget _buildRequestsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_add,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد طلبات انضمام',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'ستظهر هنا طلبات الانضمام للمجموعة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivitiesTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildActivityItem(
          icon: Icons.group_add,
          title: 'انضم أحمد محمد للمجموعة',
          time: 'منذ ساعتين',
          color: Colors.green,
        ),
        _buildActivityItem(
          icon: Icons.post_add,
          title: 'نشر سارة أحمد منشوراً جديداً',
          time: 'منذ 3 ساعات',
          color: Colors.blue,
        ),
        _buildActivityItem(
          icon: Icons.comment,
          title: 'علق محمد علي على منشور',
          time: 'منذ 5 ساعات',
          color: Colors.orange,
        ),
        _buildActivityItem(
          icon: Icons.favorite,
          title: 'أعجب فاطمة بمنشور',
          time: 'منذ يوم',
          color: Colors.red,
        ),
      ],
    );
  }

  Widget _buildStatsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('إحصائيات عامة'),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: 'الأعضاء',
                  value: '${widget.group.memberCount}',
                  icon: Icons.group,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  title: 'المنشورات',
                  value: '${widget.group.postCount}',
                  icon: Icons.post_add,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: 'التعليقات',
                  value: '156',
                  icon: Icons.comment,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  title: 'الإعجابات',
                  value: '342',
                  icon: Icons.favorite,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          _buildSectionTitle('نشاط الأعضاء'),
          const SizedBox(height: 16),
          
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildMemberActivityRow('أحمد محمد', '15 منشور', '42 تعليق'),
                  const Divider(),
                  _buildMemberActivityRow('سارة أحمد', '8 منشور', '23 تعليق'),
                  const Divider(),
                  _buildMemberActivityRow('محمد علي', '12 منشور', '31 تعليق'),
                  const Divider(),
                  _buildMemberActivityRow('فاطمة خالد', '6 منشور', '18 تعليق'),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          _buildSectionTitle('إحصائيات زمنية'),
          const SizedBox(height: 16),
          
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildTimeStatRow('اليوم', '5 منشورات', '12 تعليق'),
                  const Divider(),
                  _buildTimeStatRow('هذا الأسبوع', '23 منشور', '67 تعليق'),
                  const Divider(),
                  _buildTimeStatRow('هذا الشهر', '89 منشور', '234 تعليق'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String time,
    required Color color,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          time,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMemberActivityRow(String name, String posts, String comments) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              name,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              posts,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              comments,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeStatRow(String period, String posts, String comments) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              period,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              posts,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              comments,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }
}
