import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/notification_provider.dart';
import '../services/notification_service.dart';
import '../theme/app_theme.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  final NotificationService _notificationService = NotificationService();
  Map<String, bool> _settings = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await _notificationService.getNotificationSettings();
      setState(() {
        _settings = settings;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _updateSetting(String key, bool value) async {
    setState(() {
      _settings[key] = value;
    });
    
    try {
      await _notificationService.updateNotificationSettings(_settings);
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ الإعدادات'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // إعادة القيمة السابقة في حالة الخطأ
      setState(() {
        _settings[key] = !value;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خطأ في حفظ الإعدادات'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'إعدادات الإشعارات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // إعدادات عامة
                  _buildSectionTitle('الإعدادات العامة'),
                  const SizedBox(height: 12),
                  _buildGeneralSettings(),
                  
                  const SizedBox(height: 24),
                  
                  // إعدادات أنواع الإشعارات
                  _buildSectionTitle('أنواع الإشعارات'),
                  const SizedBox(height: 12),
                  _buildNotificationTypeSettings(),
                  
                  const SizedBox(height: 24),
                  
                  // إعدادات الصوت والاهتزاز
                  _buildSectionTitle('الصوت والاهتزاز'),
                  const SizedBox(height: 12),
                  _buildSoundSettings(),
                  
                  const SizedBox(height: 24),
                  
                  // إعدادات الخصوصية
                  _buildSectionTitle('الخصوصية'),
                  const SizedBox(height: 12),
                  _buildPrivacySettings(),
                  
                  const SizedBox(height: 24),
                  
                  // إعدادات متقدمة
                  _buildSectionTitle('إعدادات متقدمة'),
                  const SizedBox(height: 12),
                  _buildAdvancedSettings(),
                  
                  const SizedBox(height: 32),
                  
                  // أزرار الإجراءات
                  _buildActionButtons(),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildGeneralSettings() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          _buildSettingTile(
            'push_notifications',
            'الإشعارات الفورية',
            'تلقي إشعارات فورية على الجهاز',
            Icons.notifications,
          ),
          const Divider(height: 1),
          _buildSettingTile(
            'email_notifications',
            'إشعارات البريد الإلكتروني',
            'تلقي إشعارات عبر البريد الإلكتروني',
            Icons.email,
          ),
          const Divider(height: 1),
          _buildSettingTile(
            'in_app_notifications',
            'الإشعارات داخل التطبيق',
            'عرض الإشعارات أثناء استخدام التطبيق',
            Icons.app_registration,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationTypeSettings() {
    final notificationTypes = [
      {'key': 'likes', 'title': 'الإعجابات', 'subtitle': 'عندما يعجب أحد بمنشورك', 'icon': Icons.favorite},
      {'key': 'comments', 'title': 'التعليقات', 'subtitle': 'عندما يعلق أحد على منشورك', 'icon': Icons.comment},
      {'key': 'follows', 'title': 'المتابعات', 'subtitle': 'عندما يتابعك أحد', 'icon': Icons.person_add},
      {'key': 'mentions', 'title': 'الإشارات', 'subtitle': 'عندما يذكرك أحد في منشور', 'icon': Icons.alternate_email},
      {'key': 'groups', 'title': 'المجموعات', 'subtitle': 'أنشطة المجموعات والدعوات', 'icon': Icons.group},
      {'key': 'messages', 'title': 'الرسائل', 'subtitle': 'الرسائل الخاصة الجديدة', 'icon': Icons.message},
      {'key': 'birthdays', 'title': 'أعياد الميلاد', 'subtitle': 'تذكير بأعياد ميلاد الأصدقاء', 'icon': Icons.cake},
      {'key': 'events', 'title': 'الأحداث', 'subtitle': 'دعوات الأحداث والتذكيرات', 'icon': Icons.event},
    ];

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: notificationTypes.map((type) {
          final index = notificationTypes.indexOf(type);
          return Column(
            children: [
              _buildSettingTile(
                type['key'] as String,
                type['title'] as String,
                type['subtitle'] as String,
                type['icon'] as IconData,
              ),
              if (index < notificationTypes.length - 1)
                const Divider(height: 1),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSoundSettings() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          _buildSettingTile(
            'sound',
            'الصوت',
            'تشغيل صوت عند وصول الإشعارات',
            Icons.volume_up,
          ),
          const Divider(height: 1),
          _buildSettingTile(
            'vibration',
            'الاهتزاز',
            'اهتزاز الجهاز عند وصول الإشعارات',
            Icons.vibration,
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.music_note, color: Colors.black87),
            title: const Text('نغمة الإشعارات'),
            subtitle: const Text('اختيار نغمة مخصصة'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: _showRingtoneSelector,
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacySettings() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          _buildSettingTile(
            'show_preview',
            'عرض معاينة المحتوى',
            'إظهار محتوى الإشعار في الشاشة المقفلة',
            Icons.preview,
          ),
          const Divider(height: 1),
          _buildSettingTile(
            'show_sender',
            'إظهار اسم المرسل',
            'عرض اسم المرسل في الإشعارات',
            Icons.person,
          ),
          const Divider(height: 1),
          _buildSettingTile(
            'quiet_hours',
            'الساعات الهادئة',
            'إيقاف الإشعارات في أوقات محددة',
            Icons.bedtime,
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedSettings() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          _buildSettingTile(
            'group_similar',
            'تجميع الإشعارات المتشابهة',
            'دمج الإشعارات من نفس النوع',
            Icons.group_work,
          ),
          const Divider(height: 1),
          _buildSettingTile(
            'auto_delete',
            'حذف تلقائي للإشعارات القديمة',
            'حذف الإشعارات بعد 30 يوم',
            Icons.auto_delete,
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.schedule, color: Colors.black87),
            title: const Text('تكرار الإشعارات'),
            subtitle: const Text('تكرار الإشعارات المهمة'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: _showRepeatSettings,
          ),
        ],
      ),
    );
  }

  Widget _buildSettingTile(String key, String title, String subtitle, IconData icon) {
    return SwitchListTile(
      secondary: Icon(icon, color: Colors.black87),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: Text(subtitle),
      value: _settings[key] ?? true,
      onChanged: (value) => _updateSetting(key, value),
      activeColor: AppTheme.primaryColor,
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // زر إعادة تعيين جميع الإعدادات
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton.icon(
            onPressed: _resetAllSettings,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة تعيين جميع الإعدادات'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.primaryColor,
              side: BorderSide(color: AppTheme.primaryColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // زر اختبار الإشعارات
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton.icon(
            onPressed: _testNotification,
            icon: const Icon(Icons.notifications_active),
            label: const Text('اختبار الإشعارات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showRingtoneSelector() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختيار نغمة الإشعارات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('النغمة الافتراضية'),
              leading: Radio(value: 'default', groupValue: 'default', onChanged: (_) {}),
            ),
            ListTile(
              title: const Text('نغمة مخصصة'),
              leading: Radio(value: 'custom', groupValue: 'default', onChanged: (_) {}),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showRepeatSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات التكرار'),
        content: const Text('ميزة تكرار الإشعارات قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _resetAllSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text('هل تريد إعادة تعيين جميع إعدادات الإشعارات إلى القيم الافتراضية؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              
              // إعادة تعيين جميع الإعدادات
              final defaultSettings = <String, bool>{};
              for (final key in _settings.keys) {
                defaultSettings[key] = true;
              }
              
              await _notificationService.updateNotificationSettings(defaultSettings);
              setState(() {
                _settings = defaultSettings;
              });
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إعادة تعيين جميع الإعدادات'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('إعادة تعيين', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _testNotification() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إرسال إشعار تجريبي'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
