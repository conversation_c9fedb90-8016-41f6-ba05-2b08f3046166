import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/group.dart';
import '../providers/social_provider.dart';
import '../theme/app_theme.dart';
import '../widgets/group_header.dart';
import '../widgets/group_tabs.dart';
import '../widgets/group_posts_tab.dart';
import '../widgets/group_members_tab.dart';
import '../widgets/group_about_tab.dart';
import '../widgets/group_photos_tab.dart';
import '../widgets/group_events_tab.dart';

class GroupDetailScreen extends StatefulWidget {
  final Group group;

  const GroupDetailScreen({
    super.key,
    required this.group,
  });

  @override
  State<GroupDetailScreen> createState() => _GroupDetailScreenState();
}

class _GroupDetailScreenState extends State<GroupDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;
  bool _isHeaderCollapsed = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    
    // تحميل بيانات المجموعة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<SocialProvider>(context, listen: false)
          .loadGroupDetails(widget.group.id);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    const double threshold = 200.0;
    final bool shouldCollapse = _scrollController.offset > threshold;
    
    if (shouldCollapse != _isHeaderCollapsed) {
      setState(() {
        _isHeaderCollapsed = shouldCollapse;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<SocialProvider>(
        builder: (context, socialProvider, child) {
          // البحث عن المجموعة المحدثة
          final updatedGroup = socialProvider.groups.firstWhere(
            (g) => g.id == widget.group.id,
            orElse: () => widget.group,
          );

          return CustomScrollView(
            controller: _scrollController,
            slivers: [
              // شريط التطبيق مع صورة الغلاف
              SliverAppBar(
                expandedHeight: 300.0,
                floating: false,
                pinned: true,
                backgroundColor: Theme.of(context).primaryColor,
                flexibleSpace: FlexibleSpaceBar(
                  title: _isHeaderCollapsed
                      ? Text(
                          updatedGroup.name,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : null,
                  background: GroupHeader(group: updatedGroup),
                ),
                actions: [
                  // زر الإعدادات
                  if (updatedGroup.isUserAdmin('current_user'))
                    IconButton(
                      icon: const Icon(Icons.settings, color: Colors.white),
                      onPressed: () => _showGroupSettings(updatedGroup),
                    ),
                  
                  // زر المشاركة
                  IconButton(
                    icon: const Icon(Icons.share, color: Colors.white),
                    onPressed: () => _shareGroup(updatedGroup),
                  ),
                  
                  // زر المزيد
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert, color: Colors.white),
                    onSelected: (value) => _handleMenuAction(value, updatedGroup),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'notifications',
                        child: ListTile(
                          leading: Icon(Icons.notifications),
                          title: Text('إعدادات الإشعارات'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'report',
                        child: ListTile(
                          leading: Icon(Icons.report),
                          title: Text('الإبلاغ عن المجموعة'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      if (!updatedGroup.isUserMember('current_user'))
                        const PopupMenuItem(
                          value: 'join',
                          child: ListTile(
                            leading: Icon(Icons.group_add),
                            title: Text('الانضمام للمجموعة'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        )
                      else
                        const PopupMenuItem(
                          value: 'leave',
                          child: ListTile(
                            leading: Icon(Icons.exit_to_app),
                            title: Text('مغادرة المجموعة'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                    ],
                  ),
                ],
              ),

              // التبويبات
              SliverPersistentHeader(
                pinned: true,
                delegate: _SliverTabBarDelegate(
                  TabBar(
                    controller: _tabController,
                    isScrollable: true,
                    indicatorColor: Theme.of(context).primaryColor,
                    labelColor: Theme.of(context).primaryColor,
                    unselectedLabelColor: Colors.grey,
                    tabs: const [
                      Tab(text: 'المناقشات'),
                      Tab(text: 'حول'),
                      Tab(text: 'الأعضاء'),
                      Tab(text: 'الصور'),
                      Tab(text: 'الأحداث'),
                    ],
                  ),
                ),
              ),

              // محتوى التبويبات
              SliverFillRemaining(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    GroupPostsTab(group: updatedGroup),
                    GroupAboutTab(group: updatedGroup),
                    GroupMembersTab(group: updatedGroup),
                    GroupPhotosTab(group: updatedGroup),
                    GroupEventsTab(group: updatedGroup),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showGroupSettings(Group group) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        expand: false,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // رأس الحوار
              Row(
                children: [
                  Icon(Icons.settings, color: AppTheme.primaryColor),
                  const SizedBox(width: 12),
                  const Text(
                    'إعدادات المجموعة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // قائمة الإعدادات
              Expanded(
                child: ListView(
                  controller: scrollController,
                  children: [
                    // إعدادات عامة
                    _buildSettingsSection(
                      'إعدادات عامة',
                      [
                        _buildSettingsTile(
                          Icons.edit,
                          'تعديل معلومات المجموعة',
                          'تغيير الاسم والوصف والصورة',
                          () => _editGroupInfo(group),
                        ),
                        _buildSettingsTile(
                          Icons.image,
                          'تغيير صورة الغلاف',
                          'تحديث صورة غلاف المجموعة',
                          () => _changeGroupCover(group),
                        ),
                        _buildSettingsTile(
                          Icons.category,
                          'تغيير التصنيف',
                          'تحديد مجال المجموعة',
                          () => _changeGroupCategory(group),
                        ),
                      ],
                    ),

                    // إعدادات الخصوصية
                    _buildSettingsSection(
                      'الخصوصية والأمان',
                      [
                        _buildSettingsTile(
                          Icons.lock,
                          'نوع المجموعة',
                          'عامة، خاصة، أو سرية',
                          () => _changeGroupPrivacy(group),
                        ),
                        _buildSettingsTile(
                          Icons.approval,
                          'موافقة على المنشورات',
                          'مراجعة المنشورات قبل النشر',
                          () => _togglePostApproval(group),
                        ),
                        _buildSettingsTile(
                          Icons.person_add,
                          'من يمكنه دعوة أعضاء',
                          'تحديد صلاحيات الدعوة',
                          () => _changeInvitePermissions(group),
                        ),
                      ],
                    ),

                    // إعدادات الأعضاء
                    _buildSettingsSection(
                      'إدارة الأعضاء',
                      [
                        _buildSettingsTile(
                          Icons.people,
                          'إدارة الأعضاء',
                          'عرض وإدارة أعضاء المجموعة',
                          () => _manageMembers(group),
                        ),
                        _buildSettingsTile(
                          Icons.block,
                          'الأعضاء المحظورون',
                          'عرض قائمة الأعضاء المحظورين',
                          () => _viewBlockedMembers(group),
                        ),
                        _buildSettingsTile(
                          Icons.admin_panel_settings,
                          'المديرون والمشرفون',
                          'إدارة أدوار الأعضاء',
                          () => _manageAdmins(group),
                        ),
                      ],
                    ),

                    // إعدادات المحتوى
                    _buildSettingsSection(
                      'المحتوى والقوانين',
                      [
                        _buildSettingsTile(
                          Icons.rule,
                          'قوانين المجموعة',
                          'وضع وتعديل قوانين المجموعة',
                          () => _editGroupRules(group),
                        ),
                        _buildSettingsTile(
                          Icons.filter_alt,
                          'فلترة المحتوى',
                          'إعدادات فلترة المحتوى التلقائي',
                          () => _contentFiltering(group),
                        ),
                        _buildSettingsTile(
                          Icons.report,
                          'البلاغات والشكاوى',
                          'مراجعة البلاغات المرسلة',
                          () => _viewReports(group),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _shareGroup(Group group) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس الحوار
            Row(
              children: [
                Icon(Icons.share, color: AppTheme.primaryColor),
                const SizedBox(width: 12),
                const Text(
                  'مشاركة المجموعة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // خيارات المشاركة
            ListTile(
              leading: const Icon(Icons.link, color: Colors.blue),
              title: const Text('نسخ الرابط'),
              subtitle: const Text('نسخ رابط المجموعة للمشاركة'),
              onTap: () {
                Navigator.pop(context);
                _copyGroupLink(group);
              },
            ),

            ListTile(
              leading: const Icon(Icons.message, color: Colors.green),
              title: const Text('مشاركة في رسالة'),
              subtitle: const Text('إرسال رابط المجموعة في رسالة'),
              onTap: () {
                Navigator.pop(context);
                _shareInMessage(group);
              },
            ),

            ListTile(
              leading: const Icon(Icons.facebook, color: Colors.indigo),
              title: const Text('مشاركة على فيسبوك'),
              subtitle: const Text('نشر المجموعة على فيسبوك'),
              onTap: () {
                Navigator.pop(context);
                _shareOnFacebook(group);
              },
            ),

            ListTile(
              leading: const Icon(Icons.share, color: Colors.orange),
              title: const Text('مشاركة خارجية'),
              subtitle: const Text('مشاركة عبر تطبيقات أخرى'),
              onTap: () {
                Navigator.pop(context);
                _shareExternal(group);
              },
            ),

            const SizedBox(height: 10),

            // زر الإلغاء
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _copyGroupLink(Group group) {
    // محاكاة نسخ الرابط
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text('تم نسخ رابط مجموعة ${group.name} 🔗'),
          ],
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _shareInMessage(Group group) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('مشاركة ${group.name} في رسالة 💬'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _shareOnFacebook(Group group) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('مشاركة ${group.name} على فيسبوك 📘'),
        backgroundColor: Colors.indigo,
      ),
    );
  }

  void _shareExternal(Group group) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('مشاركة ${group.name} خارجياً 📤'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _handleMenuAction(String action, Group group) {
    switch (action) {
      case 'notifications':
        _showNotificationSettings(group);
        break;
      case 'report':
        _reportGroup(group);
        break;
      case 'join':
        _joinGroup(group);
        break;
      case 'leave':
        _leaveGroup(group);
        break;
    }
  }

  void _showNotificationSettings(Group group) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.notifications, color: AppTheme.primaryColor),
            const SizedBox(width: 12),
            const Text('إعدادات الإشعارات'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('إشعارات المنشورات'),
              subtitle: const Text('تلقي إشعار عند نشر منشور جديد'),
              value: true,
              onChanged: (value) {
                // TODO: حفظ الإعداد
              },
            ),
            SwitchListTile(
              title: const Text('إشعارات الأعضاء'),
              subtitle: const Text('تلقي إشعار عند انضمام عضو جديد'),
              value: false,
              onChanged: (value) {
                // TODO: حفظ الإعداد
              },
            ),
            SwitchListTile(
              title: const Text('إشعارات الأحداث'),
              subtitle: const Text('تلقي إشعار عند إنشاء حدث جديد'),
              value: true,
              onChanged: (value) {
                // TODO: حفظ الإعداد
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حفظ إعدادات الإشعارات ✅'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _reportGroup(Group group) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.report, color: Colors.red),
            const SizedBox(width: 12),
            const Text('الإبلاغ عن المجموعة'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('سبب الإبلاغ عن مجموعة "${group.name}":'),
            const SizedBox(height: 16),

            RadioListTile<String>(
              title: const Text('محتوى غير مناسب'),
              value: 'inappropriate',
              groupValue: null,
              onChanged: (value) {},
            ),
            RadioListTile<String>(
              title: const Text('رسائل مزعجة'),
              value: 'spam',
              groupValue: null,
              onChanged: (value) {},
            ),
            RadioListTile<String>(
              title: const Text('انتهاك حقوق الطبع'),
              value: 'copyright',
              groupValue: null,
              onChanged: (value) {},
            ),
            RadioListTile<String>(
              title: const Text('أخرى'),
              value: 'other',
              groupValue: null,
              onChanged: (value) {},
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال البلاغ بنجاح 📝'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إرسال البلاغ', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _joinGroup(Group group) {
    Provider.of<SocialProvider>(context, listen: false).joinGroup(group.id);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تم الانضمام لمجموعة ${group.name}')),
    );
  }

  void _leaveGroup(Group group) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مغادرة المجموعة'),
        content: Text('هل أنت متأكد من مغادرة مجموعة ${group.name}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<SocialProvider>(context, listen: false)
                  .leaveGroup(group.id);
              Navigator.pop(context);
            },
            child: const Text('مغادرة'),
          ),
        ],
      ),
    );
  }

  // دوال مساعدة لبناء واجهة الإعدادات
  Widget _buildSettingsSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ),
        Card(
          margin: const EdgeInsets.symmetric(horizontal: 8),
          child: Column(children: children),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildSettingsTile(
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.primaryColor),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  // دوال الإعدادات المختلفة
  void _editGroupInfo(Group group) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل معلومات المجموعة 📝')),
    );
  }

  void _changeGroupCover(Group group) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تغيير صورة الغلاف 🖼️')),
    );
  }

  void _changeGroupCategory(Group group) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تغيير التصنيف 📂')),
    );
  }

  void _changeGroupPrivacy(Group group) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تغيير نوع المجموعة 🔒')),
    );
  }

  void _togglePostApproval(Group group) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تغيير إعدادات الموافقة ✅')),
    );
  }

  void _changeInvitePermissions(Group group) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تغيير صلاحيات الدعوة 👥')),
    );
  }

  void _manageMembers(Group group) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إدارة الأعضاء 👥')),
    );
  }

  void _viewBlockedMembers(Group group) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('الأعضاء المحظورون 🚫')),
    );
  }

  void _manageAdmins(Group group) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إدارة المديرين والمشرفين 👑')),
    );
  }

  void _editGroupRules(Group group) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل قوانين المجموعة 📋')),
    );
  }

  void _contentFiltering(Group group) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات فلترة المحتوى 🔍')),
    );
  }

  void _viewReports(Group group) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مراجعة البلاغات 📊')),
    );
  }
}

class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverTabBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverTabBarDelegate oldDelegate) {
    return false;
  }
}
