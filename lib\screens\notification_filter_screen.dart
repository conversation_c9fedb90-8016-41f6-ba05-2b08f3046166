import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/notification_provider.dart';
import '../models/notification.dart';
import '../theme/app_theme.dart';

class NotificationFilterScreen extends StatefulWidget {
  const NotificationFilterScreen({super.key});

  @override
  State<NotificationFilterScreen> createState() => _NotificationFilterScreenState();
}

class _NotificationFilterScreenState extends State<NotificationFilterScreen> {
  final Map<NotificationType, bool> _selectedFilters = {};
  bool _showUnreadOnly = false;
  String _selectedTimeRange = 'all';
  
  @override
  void initState() {
    super.initState();
    // تهيئة الفلاتر
    for (final type in NotificationType.values) {
      _selectedFilters[type] = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'فلترة الإشعارات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          TextButton(
            onPressed: _applyFilters,
            child: const Text(
              'تطبيق',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // فلتر حسب الحالة
            _buildSectionTitle('حالة الإشعارات'),
            const SizedBox(height: 12),
            _buildUnreadFilter(),
            
            const SizedBox(height: 24),
            
            // فلتر حسب الوقت
            _buildSectionTitle('الفترة الزمنية'),
            const SizedBox(height: 12),
            _buildTimeRangeFilter(),
            
            const SizedBox(height: 24),
            
            // فلتر حسب نوع الإشعار
            _buildSectionTitle('أنواع الإشعارات'),
            const SizedBox(height: 12),
            _buildNotificationTypeFilters(),
            
            const SizedBox(height: 32),
            
            // أزرار الإجراءات
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildUnreadFilter() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: SwitchListTile(
        title: const Text(
          'إظهار غير المقروءة فقط',
          style: TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: const Text('عرض الإشعارات غير المقروءة فقط'),
        value: _showUnreadOnly,
        onChanged: (value) {
          setState(() {
            _showUnreadOnly = value;
          });
        },
        activeColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildTimeRangeFilter() {
    final timeRanges = [
      {'value': 'all', 'label': 'جميع الأوقات'},
      {'value': 'today', 'label': 'اليوم'},
      {'value': 'week', 'label': 'هذا الأسبوع'},
      {'value': 'month', 'label': 'هذا الشهر'},
    ];

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: timeRanges.map((range) {
          return RadioListTile<String>(
            title: Text(range['label']!),
            value: range['value']!,
            groupValue: _selectedTimeRange,
            onChanged: (value) {
              setState(() {
                _selectedTimeRange = value!;
              });
            },
            activeColor: AppTheme.primaryColor,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildNotificationTypeFilters() {
    final notificationTypes = [
      {'type': NotificationType.like, 'label': 'الإعجابات', 'icon': '❤️'},
      {'type': NotificationType.comment, 'label': 'التعليقات', 'icon': '💬'},
      {'type': NotificationType.reply, 'label': 'الردود', 'icon': '↩️'},
      {'type': NotificationType.share, 'label': 'المشاركات', 'icon': '🔄'},
      {'type': NotificationType.mention, 'label': 'الإشارات', 'icon': '🏷️'},
      {'type': NotificationType.follow, 'label': 'المتابعات', 'icon': '👥'},
      {'type': NotificationType.friendRequest, 'label': 'طلبات الصداقة', 'icon': '🤝'},
      {'type': NotificationType.groupPost, 'label': 'منشورات المجموعات', 'icon': '👥'},
      {'type': NotificationType.groupInvite, 'label': 'دعوات المجموعات', 'icon': '📨'},
      {'type': NotificationType.groupJoinRequest, 'label': 'طلبات الانضمام', 'icon': '📋'},
      {'type': NotificationType.birthday, 'label': 'أعياد الميلاد', 'icon': '🎂'},
      {'type': NotificationType.memory, 'label': 'الذكريات', 'icon': '📸'},
      {'type': NotificationType.event, 'label': 'الأحداث', 'icon': '📅'},
      {'type': NotificationType.message, 'label': 'الرسائل', 'icon': '💌'},
    ];

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          // زر تحديد/إلغاء تحديد الكل
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'أنواع الإشعارات',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Row(
                  children: [
                    TextButton(
                      onPressed: _selectAllTypes,
                      child: Text(
                        'تحديد الكل',
                        style: TextStyle(color: AppTheme.primaryColor),
                      ),
                    ),
                    TextButton(
                      onPressed: _deselectAllTypes,
                      child: Text(
                        'إلغاء الكل',
                        style: TextStyle(color: AppTheme.primaryColor),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // قائمة أنواع الإشعارات
          ...notificationTypes.map((typeData) {
            final type = typeData['type'] as NotificationType;
            return CheckboxListTile(
              title: Row(
                children: [
                  Text(
                    typeData['icon'] as String,
                    style: const TextStyle(fontSize: 20),
                  ),
                  const SizedBox(width: 12),
                  Text(typeData['label'] as String),
                ],
              ),
              value: _selectedFilters[type] ?? false,
              onChanged: (value) {
                setState(() {
                  _selectedFilters[type] = value ?? false;
                });
              },
              activeColor: AppTheme.primaryColor,
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // زر تطبيق الفلاتر
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _applyFilters,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'تطبيق الفلاتر',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // زر إعادة تعيين
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton(
            onPressed: _resetFilters,
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.primaryColor,
              side: BorderSide(color: AppTheme.primaryColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'إعادة تعيين',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _selectAllTypes() {
    setState(() {
      for (final type in NotificationType.values) {
        _selectedFilters[type] = true;
      }
    });
  }

  void _deselectAllTypes() {
    setState(() {
      for (final type in NotificationType.values) {
        _selectedFilters[type] = false;
      }
    });
  }

  void _resetFilters() {
    setState(() {
      _showUnreadOnly = false;
      _selectedTimeRange = 'all';
      for (final type in NotificationType.values) {
        _selectedFilters[type] = true;
      }
    });
  }

  void _applyFilters() {
    final provider = Provider.of<NotificationProvider>(context, listen: false);
    
    // تطبيق الفلاتر
    provider.applyAdvancedFilters(
      selectedTypes: _selectedFilters.entries
          .where((entry) => entry.value)
          .map((entry) => entry.key)
          .toList(),
      showUnreadOnly: _showUnreadOnly,
      timeRange: _selectedTimeRange,
    );
    
    // العودة للشاشة السابقة
    Navigator.pop(context);
    
    // إظهار رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تطبيق الفلاتر بنجاح'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
