import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../providers/notification_provider.dart';
import '../models/notification.dart';
import '../theme/app_theme.dart';
import '../widgets/smart_avatar.dart';
import 'facebook_profile_screen.dart';
import 'notification_filter_screen.dart';
import 'notification_settings_screen.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<NotificationProvider>(context, listen: false).loadNotifications();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'الإشعارات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        actions: [
          Consumer<NotificationProvider>(
            builder: (context, provider, child) {
              return IconButton(
                icon: const Icon(Icons.done_all, color: Colors.white),
                onPressed: provider.unreadCount > 0
                    ? () => provider.markAllAsRead()
                    : null,
                tooltip: 'تحديد الكل كمقروء',
              );
            },
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'filter',
                child: Row(
                  children: [
                    Icon(Icons.filter_list, color: Colors.black),
                    SizedBox(width: 8),
                    Text('فلترة الإشعارات'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, color: Colors.black),
                    SizedBox(width: 8),
                    Text('إعدادات الإشعارات'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Consumer<NotificationProvider>(
              builder: (context, provider, child) {
                return Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('الكل'),
                      if (provider.unreadCount > 0) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '${provider.unreadCount}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              },
            ),
            const Tab(text: 'مجمعة'),
          ],
        ),
      ),
      body: Consumer<NotificationProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading && provider.notifications.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (provider.errorMessage != null) {
            return _buildErrorState(provider);
          }

          if (provider.notifications.isEmpty) {
            return _buildEmptyState();
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildAllNotifications(provider),
              _buildGroupedNotifications(provider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildErrorState(NotificationProvider provider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            provider.errorMessage!,
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => provider.loadNotifications(),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد إشعارات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'ستظهر إشعاراتك هنا عند وصولها',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildAllNotifications(NotificationProvider provider) {
    return RefreshIndicator(
      onRefresh: () => provider.refreshNotifications(),
      child: Column(
        children: [
          // شريط الفلترة
          _buildFilterBar(provider),

          // قائمة الإشعارات
          Expanded(
            child: ListView.builder(
              itemCount: provider.notifications.length,
              itemBuilder: (context, index) {
                final notification = provider.notifications[index];
                return _NotificationItem(
                  notification: notification,
                  onTap: () => _handleNotificationTap(notification),
                  onDismiss: () => provider.deleteNotification(notification.id),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGroupedNotifications(NotificationProvider provider) {
    final groupedNotifications = provider.groupedNotifications;

    return RefreshIndicator(
      onRefresh: () => provider.refreshNotifications(),
      child: ListView.builder(
        itemCount: groupedNotifications.length,
        itemBuilder: (context, index) {
          final group = groupedNotifications[index];
          return _GroupedNotificationItem(
            group: group,
            onTap: () => _handleGroupTap(group),
          );
        },
      ),
    );
  }

  Widget _buildFilterBar(NotificationProvider provider) {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip(
            'الكل',
            provider.selectedFilter == null,
            () => provider.filterByType(null),
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            'الإعجابات',
            provider.selectedFilter == NotificationType.like,
            () => provider.filterByType(NotificationType.like),
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            'التعليقات',
            provider.selectedFilter == NotificationType.comment,
            () => provider.filterByType(NotificationType.comment),
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            'المتابعات',
            provider.selectedFilter == NotificationType.follow,
            () => provider.filterByType(NotificationType.follow),
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            'المجموعات',
            provider.selectedFilter == NotificationType.groupPost,
            () => provider.filterByType(NotificationType.groupPost),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.grey[200],
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black87,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'filter':
        _showFilterDialog();
        break;
      case 'settings':
        _showNotificationSettings();
        break;
    }
  }

  void _showFilterDialog() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const NotificationFilterScreen(),
      ),
    );
  }

  void _showNotificationSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const NotificationSettingsScreen(),
      ),
    );
  }

  void _handleNotificationTap(AppNotification notification) {
    // تحديد الإشعار كمقروء
    Provider.of<NotificationProvider>(context, listen: false)
        .markAsRead(notification.id);

    // التوجه حسب نوع الإشعار
    switch (notification.type) {
      case NotificationType.like:
      case NotificationType.comment:
      case NotificationType.share:
        // فتح المنشور
        if (notification.postId != null) {
          _navigateToPost(notification.postId!);
        }
        break;
      case NotificationType.follow:
        // فتح الملف الشخصي
        _navigateToProfile(notification.fromUserId);
        break;
      case NotificationType.groupInvite:
      case NotificationType.groupPost:
        // فتح المجموعة
        if (notification.groupId != null) {
          _navigateToGroup(notification.groupId!);
        }
        break;
      default:
        break;
    }
  }

  void _handleGroupTap(GroupedNotification group) {
    // معالجة النقر على المجموعة
    if (group.notifications.isNotEmpty) {
      _handleNotificationTap(group.notifications.first);
    }
  }

  void _navigateToPost(String postId) {
    // التوجه للمنشور
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('فتح المنشور: $postId')),
    );
  }

  void _navigateToProfile(String userId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FacebookProfileScreen(
          userId: userId,
          userName: 'المستخدم',
        ),
      ),
    );
  }

  void _navigateToGroup(String groupId) {
    // التوجه للمجموعة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('فتح المجموعة: $groupId')),
    );
  }
}

// Widget للإشعار الفردي
class _NotificationItem extends StatelessWidget {
  final AppNotification notification;
  final VoidCallback onTap;
  final VoidCallback onDismiss;

  const _NotificationItem({
    required this.notification,
    required this.onTap,
    required this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      onDismissed: (_) => onDismiss(),
      background: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        child: const Icon(
          Icons.delete,
          color: Colors.white,
        ),
      ),
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: _getNotificationColor(notification.type),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              notification.typeIcon,
              style: const TextStyle(fontSize: 20),
            ),
          ),
        ),
        title: Text(
          notification.title,
          style: TextStyle(
            fontWeight: notification.isUnread ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.message),
            const SizedBox(height: 4),
            Text(
              notification.timeAgo,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        trailing: notification.isUnread
            ? Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                ),
              )
            : null,
        onTap: onTap,
      ),
    );
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.like:
        return Colors.red.withValues(alpha: 0.1);
      case NotificationType.comment:
        return Colors.blue.withValues(alpha: 0.1);
      case NotificationType.follow:
        return Colors.red.withValues(alpha: 0.1);
      case NotificationType.groupPost:
        return Colors.purple.withValues(alpha: 0.1);
      case NotificationType.groupInvite:
        return Colors.orange.withValues(alpha: 0.1);
      default:
        return Colors.grey.withValues(alpha: 0.1);
    }
  }
}

// Widget للإشعارات المجمعة
class _GroupedNotificationItem extends StatelessWidget {
  final GroupedNotification group;
  final VoidCallback onTap;

  const _GroupedNotificationItem({
    required this.group,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Stack(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: _getNotificationColor(group.type),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                group.notifications.first.typeIcon,
                style: const TextStyle(fontSize: 20),
              ),
            ),
          ),
          if (group.count > 1)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: Text(
                  '${group.count}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
      title: Text(
        group.groupTitle,
        style: TextStyle(
          fontWeight: group.hasUnread ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(group.groupBody),
          const SizedBox(height: 4),
          Text(
            group.timeAgo,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
      trailing: group.hasUnread
          ? Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
              ),
            )
          : null,
      onTap: onTap,
    );
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.like:
        return Colors.red.withValues(alpha: 0.1);
      case NotificationType.comment:
        return Colors.blue.withValues(alpha: 0.1);
      case NotificationType.follow:
        return Colors.red.withValues(alpha: 0.1);
      case NotificationType.groupPost:
        return Colors.purple.withValues(alpha: 0.1);
      case NotificationType.groupInvite:
        return Colors.orange.withValues(alpha: 0.1);
      default:
        return Colors.grey.withValues(alpha: 0.1);
    }
  }
}