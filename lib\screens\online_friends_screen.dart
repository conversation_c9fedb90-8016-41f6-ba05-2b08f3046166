import 'package:flutter/material.dart';
import '../models/online_friend.dart';
import '../widgets/online_friends_widget.dart';


class OnlineFriendsScreen extends StatefulWidget {
  const OnlineFriendsScreen({super.key});

  @override
  State<OnlineFriendsScreen> createState() => _OnlineFriendsScreenState();
}

class _OnlineFriendsScreenState extends State<OnlineFriendsScreen> {
  late OnlineFriendsData _friendsData;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _friendsData = MockOnlineFriendsData.getMockOnlineFriendsData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الأصدقاء المتصلين',
              style: TextStyle(
                color: Colors.black,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '${_friendsData.totalOnlineCount} متصل الآن',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Colors.black),
            onPressed: _showSearchDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // إحصائيات سريعة
          _buildQuickStats(),
          
          // قائمة الأصدقاء
          Expanded(
            child: _buildFriendsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    final onlineCount = _friendsData.onlineFriends.where((f) => f.isOnline).length;
    final awayCount = _friendsData.onlineFriends.where((f) => f.isAway).length;
    final busyCount = _friendsData.onlineFriends.where((f) => f.isBusy).length;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildStatItem(
            icon: Icons.circle,
            color: Colors.green,
            label: 'متصل',
            count: onlineCount,
          ),
          const SizedBox(width: 20),
          _buildStatItem(
            icon: Icons.circle,
            color: Colors.orange,
            label: 'بعيد',
            count: awayCount,
          ),
          const SizedBox(width: 20),
          _buildStatItem(
            icon: Icons.circle,
            color: Colors.red,
            label: 'مشغول',
            count: busyCount,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required Color color,
    required String label,
    required int count,
  }) {
    return Expanded(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 12),
              const SizedBox(width: 4),
              Text(
                count.toString(),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFriendsList() {
    final filteredFriends = _getFilteredFriends();
    
    if (filteredFriends.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filteredFriends.length,
      itemBuilder: (context, index) {
        final friend = filteredFriends[index];
        return _buildDetailedFriendItem(friend);
      },
    );
  }

  Widget _buildDetailedFriendItem(OnlineFriend friend) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _startChat(friend),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // صورة الملف الشخصي مع مؤشر الحالة
              Stack(
                children: [
                  CircleAvatar(
                    radius: 28,
                    backgroundColor: Colors.grey[300],
                    backgroundImage: friend.profileImageUrl != null
                        ? NetworkImage(friend.profileImageUrl!)
                        : null,
                    child: friend.profileImageUrl == null
                        ? Icon(
                            Icons.person,
                            color: Colors.grey[600],
                            size: 32,
                          )
                        : null,
                  ),
                  
                  // مؤشر الحالة
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 18,
                      height: 18,
                      decoration: BoxDecoration(
                        color: _getStatusColor(friend.status),
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 3),
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(width: 16),
              
              // معلومات الصديق
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      friend.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        if (friend.isTyping) ...[
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'يكتب رسالة...',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.blue,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ] else ...[
                          Icon(
                            Icons.circle,
                            size: 8,
                            color: _getStatusColor(friend.status),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            friend.currentActivity ?? friend.statusText,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              
              // أزرار الإجراءات
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // زر الرسائل
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: () => _startChat(friend),
                      icon: const Icon(
                        Icons.message,
                        color: Colors.blue,
                        size: 20,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                  
                  const SizedBox(width: 8),
                  
                  // زر الاتصال
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: () => _makeCall(friend),
                      icon: const Icon(
                        Icons.phone,
                        color: Colors.red,
                        size: 20,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                  
                  const SizedBox(width: 8),
                  
                  // زر الاتصال المرئي
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.purple.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: () => _makeVideoCall(friend),
                      icon: const Icon(
                        Icons.videocam,
                        color: Colors.purple,
                        size: 20,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty 
                ? 'لا يوجد أصدقاء متصلين'
                : 'لا توجد نتائج للبحث',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty
                ? 'سيظهر أصدقاؤك المتصلين هنا'
                : 'جرب البحث بكلمات مختلفة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<OnlineFriend> _getFilteredFriends() {
    final allFriends = _friendsData.allFriends;
    
    if (_searchQuery.isEmpty) {
      return allFriends;
    }
    
    return allFriends.where((friend) {
      return friend.name.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  Color _getStatusColor(OnlineStatus status) {
    switch (status) {
      case OnlineStatus.online:
        return Colors.red;
      case OnlineStatus.away:
        return Colors.orange;
      case OnlineStatus.busy:
        return Colors.red;
      case OnlineStatus.offline:
        return Colors.grey;
    }
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث عن صديق'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'اكتب اسم الصديق...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
                _searchController.clear();
              });
              Navigator.pop(context);
            },
            child: const Text('مسح'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _startChat(OnlineFriend friend) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('بدء محادثة مع ${friend.name}'),
        action: SnackBarAction(
          label: 'فتح',
          onPressed: () {
            // هنا يمكن إضافة الانتقال لشاشة الدردشة
          },
        ),
      ),
    );
  }

  void _makeCall(OnlineFriend friend) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('اتصال صوتي مع ${friend.name}'),
        action: SnackBarAction(
          label: 'إلغاء',
          onPressed: () {},
        ),
      ),
    );
  }

  void _makeVideoCall(OnlineFriend friend) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('اتصال مرئي مع ${friend.name}'),
        action: SnackBarAction(
          label: 'إلغاء',
          onPressed: () {},
        ),
      ),
    );
  }
}
